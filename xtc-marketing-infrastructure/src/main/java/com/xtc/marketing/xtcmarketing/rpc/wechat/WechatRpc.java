package com.xtc.marketing.xtcmarketing.rpc.wechat;

import com.xtc.marketing.xtcmarketing.exception.SysErrorCode;
import com.xtc.marketing.xtcmarketing.exception.SysException;
import com.xtc.marketing.xtcmarketing.rpc.wechat.response.BaseWechatResponse;
import com.xtc.marketing.xtcmarketing.rpc.wechat.response.GetUserInfoResponse;
import com.xtc.marketing.xtcmarketing.util.GsonUtil;
import com.xtc.marketing.xtcmarketing.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 微信RPC
 */
@Slf4j
@Component
public class WechatRpc {

    /**
     * 获取企业微信用户信息
     * <p><a href="https://developer.work.weixin.qq.com/document/path/96442">文档</a>
     * <p>必填参数：code
     * <p>返回值：userid (企业成员), user_ticket (企业成员), openid (非企业成员), external_userid (非企业成员), errcode, errmsg
     * <p>错误码：errcode 为 0 时代表请求成功
     */
    private static final String GET_USER_INFO_GET = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=%s&code=%s";

    /**
     * 获取企业微信用户id
     *
     * @param scrmWechatId 营销接口，微信id
     * @param code         临时登录凭证 code
     * @return 企业微信用户id
     */
    public Optional<String> getUserId(String scrmWechatId, String code) {
        return this.getAccessToken(scrmWechatId)
                .map(accessToken -> {
                    String url = GET_USER_INFO_GET.formatted(accessToken, code);
                    String response = HttpUtil.get(url);
                    // TODO: 测试数据
                    response = "{\"userid\":\"865adf33751f400082b56f021d97578f\",\"errcode\":0,\"errmsg\":\"ok\"}";
                    GetUserInfoResponse user = this.parseResponse(response, GetUserInfoResponse.class);
                    return user.getUserId();
                });
    }

    /**
     * 获取小程序全局唯一后台接口调用凭据（access_token）
     * 调用公共服务项目接口获取 <a href="https://xtc-api-doc.okii.com/#/view/kXO6x98D">文档</a>
     *
     * @param scrmWechatId 营销接口，微信id
     * @return 接口调用凭据
     */
    public Optional<String> getAccessToken(String scrmWechatId) {
        String url = "https://scrmapis.okii.com/wechat/accessToken?wechatId=" + scrmWechatId;
        String response = HttpUtil.get(url);
        String accessToken = GsonUtil.getAsString(response, "data", "accessToken");
        return Optional.ofNullable(accessToken);
    }

    private <T extends BaseWechatResponse> T call(HttpMethod httpMethod, String url, Class<T> clazz) {
        String responseStr = HttpUtil.get(url);
        return this.parseResponse(responseStr, clazz);
    }

    /**
     * 微信接口返回值解析
     *
     * @param responseStr 微信接口返回值
     * @param clazz    业务类 Class
     * @param <T>      业务类
     * @return 业务类实例
     */
    private <T extends BaseWechatResponse> T parseResponse(String responseStr, Class<T> clazz) {
        T target = GsonUtil.jsonToBean(responseStr, clazz);
        if (target == null || target.failure()) {
            String msg = String.format("微信RPC异常 response: %s, url: %s", responseStr, url);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg);
        }
        return target;
    }

}
