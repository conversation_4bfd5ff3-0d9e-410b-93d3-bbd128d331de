package com.xtc.marketing.xtcmarketing.rpc.wechat.request;

import com.google.gson.annotations.Expose;
import com.xtc.marketing.xtcmarketing.rpc.wechat.response.BaseWechatResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.http.HttpMethod;

/**
 * 小天才电话手表基础请求类
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public abstract class BaseWechatRequest<T extends BaseWechatResponse> {

    /**
     * 营销接口，微信id
     */
    @Expose
    private String wechatId;
    /**
     * accessToken
     */
    @Expose
    private String accessToken;
    /**
     * HTTP 请求方法
     */
    @Expose
    private HttpMethod httpMethod;
    /**
     * 接口路径
     */
    @Expose
    private String url;

    /**
     * 获取接口路径
     *
     * @return 接口路径
     */
    protected abstract String getRequestUrl();

    /**
     * 获取 HTTP 响应类型
     *
     * @return 响应类型
     */
    public abstract Class<T> getResponseClass();

}
