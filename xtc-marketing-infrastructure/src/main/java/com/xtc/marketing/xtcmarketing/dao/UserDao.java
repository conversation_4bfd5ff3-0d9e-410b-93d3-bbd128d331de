package com.xtc.marketing.xtcmarketing.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryCondition;
import com.xtc.marketing.xtcmarketing.config.BaseDao;
import com.xtc.marketing.xtcmarketing.dao.mapper.UserMapper;
import com.xtc.marketing.xtcmarketing.dataobject.UserDO;
import com.xtc.marketing.xtcmarketing.dto.query.UserPageQry;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.xtc.marketing.xtcmarketing.dataobject.table.UserDOTableDef.USER_DO;

/**
 * 用户数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class UserDao extends BaseDao<UserMapper, UserDO> {

    /**
     * 查询用户
     *
     * @param userId 用户id
     * @return 用户
     */
    public Optional<UserDO> getByUserId(String userId) {
        if (If.noText(userId)) {
            return Optional.empty();
        }
        return queryChain()
                .where(USER_DO.USER_ID.eq(userId))
                .oneOpt();
    }

    /**
     * 查询用户分页列表
     *
     * @param qry 参数
     * @return 用户分页列表
     */
    public Page<UserDO> pageBy(UserPageQry qry) {
        Page<UserDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(USER_DO.USER_ID.eq(qry.getUserId(), If::hasText))
                .and(
                        QueryCondition.createEmpty()
                                .and("MATCH (" + USER_DO.USER_NAME.getName() + ") AGAINST (? IN BOOLEAN MODE)", "+" + qry.getUserName())
                                .when(If.hasText(qry.getUserName()))
                )
                .and(USER_DO.ENABLED.eq(qry.getEnabled(), If::notNull))
                .orderBy(USER_DO.ID.desc())
                .page(page);
    }

    /**
     * 判断用户id是否存在
     *
     * @param userId 用户id
     * @return 执行结果
     */
    public boolean existsByUserId(String userId) {
        if (If.noText(userId)) {
            return false;
        }
        return queryChain().where(USER_DO.USER_ID.eq(userId)).exists();
    }

    /**
     * 判断用户id是否存在（排除指定ID）
     *
     * @param userId    用户id
     * @param excludeId 排除的ID
     * @return 执行结果
     */
    public boolean existsByUserIdExcludeId(String userId, Long excludeId) {
        if (If.noText(userId) || If.isNull(excludeId)) {
            return false;
        }
        return queryChain()
                .where(USER_DO.USER_ID.eq(userId))
                .and(USER_DO.ID.ne(excludeId))
                .exists();
    }

}
