package com.xtc.marketing.xtcmarketing.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.xtc.marketing.xtcmarketing.config.BaseDao;
import com.xtc.marketing.xtcmarketing.dao.mapper.ShopTagMapper;
import com.xtc.marketing.xtcmarketing.dataobject.ShopTagDO;
import com.xtc.marketing.xtcmarketing.dto.query.ShopTagPageQry;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

import static com.xtc.marketing.xtcmarketing.dataobject.table.ShopTagDOTableDef.SHOP_TAG_DO;

/**
 * 门店标签数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class ShopTagDao extends BaseDao<ShopTagMapper, ShopTagDO> {

    /**
     * 查询门店标签分页列表
     *
     * @param qry 参数
     * @return 门店标签分页列表
     */
    public Page<ShopTagDO> pageBy(ShopTagPageQry qry) {
        Page<ShopTagDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(SHOP_TAG_DO.SHOP_ID.eq(qry.getShopId(), If::hasText))
                .orderBy(SHOP_TAG_DO.ID.desc())
                .page(page);
    }

    /**
     * 查询门店标签
     *
     * @param shopId 门店id
     * @return 门店标签
     */
    public Optional<ShopTagDO> getByShopId(String shopId) {
        if (If.noText(shopId)) {
            return Optional.empty();
        }
        return queryChain()
                .where(SHOP_TAG_DO.SHOP_ID.eq(shopId))
                .orderBy(SHOP_TAG_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 更新门店标签
     *
     * @param shopId 门店id
     * @param tags   标签集合
     */
    public void updateTagsByShopId(String shopId, List<ShopTagDO.Tag> tags) {
        if (If.noText(shopId)) {
            return;
        }
        updateChain()
                .set(SHOP_TAG_DO.TAGS, tags)
                .where(SHOP_TAG_DO.SHOP_ID.eq(shopId))
                .orderBy(SHOP_TAG_DO.ID.desc())
                .limit(LIMIT_ONE)
                .update();
    }

}
