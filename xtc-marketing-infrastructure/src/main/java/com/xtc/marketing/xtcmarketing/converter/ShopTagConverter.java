package com.xtc.marketing.xtcmarketing.converter;

import com.xtc.marketing.xtcmarketing.dataobject.ShopTagDO;
import com.xtc.marketing.xtcmarketing.dto.ShopTagDTO;
import org.mapstruct.*;

import java.util.List;

/**
 * 门店标签数据转换器
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        builder = @Builder(disableBuilder = true),
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface ShopTagConverter {

    ShopTagDTO doToDto(ShopTagDO source);

    List<ShopTagDTO> doToDto(List<ShopTagDO> source);

    ShopTagDTO.TagDTO tagToTagDto(ShopTagDO.Tag source);

}
