package com.xtc.xtcshop.rpc.xtcwatch;

import com.xtc.xtcshop.constant.SystemConstant;
import com.xtc.xtcshop.exception.SysErrorCode;
import com.xtc.xtcshop.exception.SysException;
import com.xtc.xtcshop.rpc.xtcwatch.request.AccessTokenGetRequest;
import com.xtc.xtcshop.rpc.xtcwatch.request.BaseXtcWatchRequest;
import com.xtc.xtcshop.rpc.xtcwatch.request.ProxyXtcWatchRequest;
import com.xtc.xtcshop.rpc.xtcwatch.response.AccessTokenGetResponse;
import com.xtc.xtcshop.rpc.xtcwatch.response.BaseXtcWatchResponse;
import com.xtc.xtcshop.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.UnaryOperator;

/**
 * 小天才电话手表 RPC
 */
@Slf4j
@Component
public class XtcWatchRpc {

    /**
     * 生产环境域名
     */
    private static final String DOMAIN = "https://api.watch.okii.com";
    /**
     * 测试环境域名
     */
    private static final String DOMAIN_TEST = "https://api-module.okii.com";
    /**
     * http 请求工具类
     */
    private static final RestTemplate REST_TEMPLATE = new RestTemplate();
    /**
     * 客户端 accessToken 缓存
     */
    private static final ConcurrentMap<String, AccessTokenGetResponse> CLIENT_TOKEN_CACHE = new ConcurrentHashMap<>();
    /**
     * 客户端id：官方商城抵用卷兑换
     */
    private static final String CLIENT_APP_ID = "100160";
    /**
     * 客户端密钥：官方商城抵用卷兑换
     */
    private static final String CLIENT_APP_SECRET = "fd5aa75d92f741ef833a1e9094c4f73c";

    /**
     * 环境配置
     */
    @Value("${spring.profiles.active}")
    private String profileActive;

    static {
        // 设置字符串的转换使用UTF-8编码
        REST_TEMPLATE.getMessageConverters().stream()
                .filter(converter -> converter.getClass().equals(StringHttpMessageConverter.class))
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));
        // 设置请求超时时间
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(3000);
        requestFactory.setReadTimeout(3000);
        REST_TEMPLATE.setRequestFactory(requestFactory);
    }

    /**
     * 获取 accessToken
     *
     * @param xtcRequest 请求
     * @param appSecret  客户端密钥
     * @return accessToken
     */
    public String getAccessToken(BaseXtcWatchRequest<?> xtcRequest, String appSecret) {
        AccessTokenGetResponse accessTokenGetResponse = CLIENT_TOKEN_CACHE.get(xtcRequest.getAppId());
        if (accessTokenGetResponse != null && accessTokenGetResponse.isNotExpire()) {
            return accessTokenGetResponse.getAccessToken();
        }
        log.warn("小天才电话手表RPC accessToken 不存在或过期 appId: {}, accessToken: {}", xtcRequest.getAppId(), accessTokenGetResponse);
        return this.refreshAccessToken(xtcRequest, appSecret);
    }

    /**
     * 刷新 accessToken
     *
     * @param xtcRequest 请求
     * @param appSecret  客户端密钥
     * @return accessToken
     */
    public String refreshAccessToken(BaseXtcWatchRequest<?> xtcRequest, String appSecret) {
        AccessTokenGetRequest qry = AccessTokenGetRequest.builder().domain(xtcRequest.getDomain()).appSecret(appSecret).build();
        AccessTokenGetResponse accessTokenGetResponse = this.call(qry);
        if (accessTokenGetResponse == null || StringUtils.isBlank(accessTokenGetResponse.getAccessToken())) {
            return null;
        }
        // 计算过期时间
        LocalDateTime expiresAt = LocalDateTime.now().plusSeconds(accessTokenGetResponse.getExpiresTime());
        accessTokenGetResponse.setExpiresAt(expiresAt);
        // 刷新缓存 accessToken
        CLIENT_TOKEN_CACHE.put(xtcRequest.getAppId(), accessTokenGetResponse);
        return accessTokenGetResponse.getAccessToken();
    }

    /**
     * 调用接口
     *
     * @param request 请求
     * @param <T>     响应类型
     * @return 响应
     */
    public <T extends BaseXtcWatchResponse> T call(BaseXtcWatchRequest<T> request) {
        return call(request, false);
    }

    /**
     * 调用接口
     *
     * @param xtcRequest   请求
     * @param refreshToken 刷新 accessToken
     * @param <T>          响应类型
     * @return 响应
     */
    public <T extends BaseXtcWatchResponse> T call(BaseXtcWatchRequest<T> xtcRequest, boolean refreshToken) {
        // 初始化请求
        RequestEntity<?> request = initRequestEntity(xtcRequest, refreshToken);

        // 发起请求
        String responseStr = "";
        String requestInfoLog = String.format("小天才电话手表RPC %s url: %s, header: %s, body: %s",
                request.getMethod(), request.getUrl(), request.getHeaders(), request.getBody());
        UnaryOperator<String> requestErrDetail = str -> String.format("小天才电话手表RPC异常 response: %s", str);
        try {
            log.info(requestInfoLog);
            ResponseEntity<String> response = REST_TEMPLATE.exchange(request, String.class);
            responseStr = response.getBody();
            log.info("{}, response: {}", requestInfoLog, responseStr);
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            if (e instanceof HttpStatusCodeException) {
                responseStr = e.getMessage();
            }
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, requestErrDetail.apply(responseStr), e);
        }

        // 解析响应结果
        return paresResponse(xtcRequest, refreshToken, responseStr, requestErrDetail);
    }

    /**
     * 初始化请求
     *
     * @param xtcRequest   请求
     * @param refreshToken 刷新 accessToken
     * @param <T>          响应类型
     * @return 请求
     */
    private <T extends BaseXtcWatchResponse> RequestEntity<?> initRequestEntity(BaseXtcWatchRequest<T> xtcRequest,
                                                                                boolean refreshToken) {
        // 设置请求客户端
        xtcRequest.setAppId(CLIENT_APP_ID);
        // 针对获取 accessToken 请求不设置 accessToken 避免死循环
        if (BooleanUtils.isTrue(refreshToken)) {
            xtcRequest.setAccessToken(this.refreshAccessToken(xtcRequest, CLIENT_APP_SECRET));
        }
        if (BooleanUtils.isFalse(refreshToken)
                && StringUtils.isBlank(xtcRequest.getAccessToken())
                && BooleanUtils.isFalse(xtcRequest instanceof AccessTokenGetRequest)) {
            xtcRequest.setAccessToken(this.getAccessToken(xtcRequest, CLIENT_APP_SECRET));
        }

        // 构建请求
        String domain = xtcRequest.getDomain();
        if (StringUtils.isBlank(domain)) {
            domain = SystemConstant.isTestProfile(profileActive) ? DOMAIN_TEST : DOMAIN;
        }
        String httpUrl = domain + xtcRequest.getUrlPath();
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(httpUrl);
        if (xtcRequest.getHttpMethod() == HttpMethod.GET) {
            uriBuilder.queryParam("appId", xtcRequest.getAppId())
                    .queryParam("accessToken", xtcRequest.getAccessToken());
        }
        RequestEntity.BodyBuilder requestBuilder = RequestEntity.method(xtcRequest.getHttpMethod(), uriBuilder.build().toUri());
        return xtcRequest.getHttpMethod() == HttpMethod.POST
                ? requestBuilder.contentType(MediaType.APPLICATION_JSON).body(GsonUtil.objectToJson(xtcRequest))
                : requestBuilder.build();
    }

    /**
     * 解析响应结果
     *
     * @param xtcRequest       请求
     * @param refreshToken     刷新 accessToken
     * @param responseStr      响应结果
     * @param requestErrDetail 请求异常详情
     * @param <T>              响应类型
     * @return 响应
     */
    private <T extends BaseXtcWatchResponse> T paresResponse(BaseXtcWatchRequest<T> xtcRequest, boolean refreshToken,
                                                             String responseStr, UnaryOperator<String> requestErrDetail) {
        T baseResponse = GsonUtil.jsonToBean(responseStr, xtcRequest.getResponseClass());
        // 当 accessToken 过期，则设置刷新并重试。如果当前 accessToken 已经刷新过，则不再重试避免死循环。
        if (baseResponse != null && baseResponse.isTokenExpire() && BooleanUtils.isFalse(refreshToken)) {
            log.info("小天才电话手表RPC accessToken 过期 appId: {}, accessToken: {}",
                    xtcRequest.getAppId(), xtcRequest.getAccessToken());
            return this.call(xtcRequest, true);
        }
        if (baseResponse == null || baseResponse.isFailure()) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, requestErrDetail.apply(responseStr));
        }
        // 针对基础响应类直接返回响应结果
        if (xtcRequest.getResponseClass() == BaseXtcWatchResponse.class || baseResponse.isDataEmpty()) {
            baseResponse.setResponseStr(responseStr);
            return baseResponse;
        }
        // 解析数据结果，放在最外层方便获取，并且不在 data 属性设置数据
        String dataJson = GsonUtil.objectToJson(baseResponse.getData());
        T xtcResponse = GsonUtil.jsonToBean(dataJson, xtcRequest.getResponseClass());
        xtcResponse.setCode(baseResponse.getCode());
        xtcResponse.setDesc(baseResponse.getDesc());
        xtcResponse.setResponseStr(responseStr);
        return xtcResponse;
    }

    public static void main(String[] args) {
        XtcWatchRpc xtcWatchRpc = new XtcWatchRpc();
        ProxyXtcWatchRequest qry = ProxyXtcWatchRequest.builder()
                .httpMethod(HttpMethod.POST)
                .domain("https://api-module.okii.com")
                .urlId("1013")
                .urlPath("/resource-service/general/forward")
                .params("{\"rows\": 10, \"model\": \"all\"}")
                .build();
        xtcWatchRpc.call(qry);
    }

}
