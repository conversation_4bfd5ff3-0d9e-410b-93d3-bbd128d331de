package com.xtc.marketing.xtcmarketing.service;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.xtcmarketing.dto.UserDTO;
import com.xtc.marketing.xtcmarketing.dto.command.UserCreateCmd;
import com.xtc.marketing.xtcmarketing.dto.command.UserEditCmd;
import com.xtc.marketing.xtcmarketing.dto.query.UserPageQry;

/**
 * 用户应用服务接口
 */
public interface UserAppService {

    /**
     * 用户分页列表
     *
     * @param qry 参数
     * @return 用户分页列表
     */
    PageResponse<UserDTO> pageUsers(UserPageQry qry);

    /**
     * 用户详情
     *
     * @param id 用户id
     * @return 用户详情
     */
    UserDTO userDetail(long id);

    /**
     * 新增用户
     *
     * @param cmd 参数
     * @return 用户id
     */
    Long createUser(UserCreateCmd cmd);

    /**
     * 修改用户
     *
     * @param id  用户id
     * @param cmd 参数
     */
    void editUser(long id, UserEditCmd cmd);

    /**
     * 删除用户
     *
     * @param id 用户id
     */
    void removeUser(long id);

}
