package com.xtc.marketing.xtcmarketing.service;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.xtcmarketing.dto.ShopTagDTO;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagCreateCmd;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagEditCmd;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagRemoveCmd;
import com.xtc.marketing.xtcmarketing.dto.query.ShopTagPageQry;

/**
 * 门店标签应用服务接口
 */
public interface ShopTagAppService {

    /**
     * 门店标签分页列表
     *
     * @param qry 参数
     * @return 门店标签分页列表
     */
    PageResponse<ShopTagDTO> pageShopTags(ShopTagPageQry qry);

    /**
     * 门店标签详情
     *
     * @param shopId 门店id
     * @return 门店标签详情
     */
    ShopTagDTO getShopTagDetail(String shopId);

    /**
     * 新增门店标签
     *
     * @param cmd 参数
     */
    void createShopTag(ShopTagCreateCmd cmd);

    /**
     * 编辑门店标签
     *
     * @param cmd 参数
     */
    void editShopTag(ShopTagEditCmd cmd);

    /**
     * 删除门店标签
     *
     * @param cmd 参数
     */
    void removeShopTag(ShopTagRemoveCmd cmd);

}
