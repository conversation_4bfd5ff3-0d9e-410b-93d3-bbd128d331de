package com.xtc.marketing.xtcmarketing.service;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.xtcmarketing.dto.TagDTO;
import com.xtc.marketing.xtcmarketing.dto.command.TagCreateCmd;
import com.xtc.marketing.xtcmarketing.dto.command.TagEditCmd;
import com.xtc.marketing.xtcmarketing.dto.query.TagPageQry;

/**
 * 标签应用服务接口
 */
public interface TagAppService {

    /**
     * 标签分页列表
     *
     * @param qry 参数
     * @return 标签分页列表
     */
    PageResponse<TagDTO> pageTags(TagPageQry qry);

    /**
     * 标签详情
     *
     * @param id 标签id
     * @return 标签详情
     */
    TagDTO tagDetail(long id);

    /**
     * 新增标签
     *
     * @param cmd 参数
     * @return 标签id
     */
    Long createTag(TagCreateCmd cmd);

    /**
     * 修改标签
     *
     * @param id  标签id
     * @param cmd 参数
     */
    void editTag(long id, TagEditCmd cmd);

    /**
     * 删除标签
     *
     * @param id 标签id
     */
    void removeTag(long id);

}
