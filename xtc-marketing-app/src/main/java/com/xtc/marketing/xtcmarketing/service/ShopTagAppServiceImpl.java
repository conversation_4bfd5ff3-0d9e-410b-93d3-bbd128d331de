package com.xtc.marketing.xtcmarketing.service;

import com.google.common.collect.Lists;
import com.mybatisflex.core.paginate.Page;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.xtcmarketing.converter.ShopTagConverter;
import com.xtc.marketing.xtcmarketing.dao.ShopTagDao;
import com.xtc.marketing.xtcmarketing.dataobject.ShopTagDO;
import com.xtc.marketing.xtcmarketing.dataobject.TagDO;
import com.xtc.marketing.xtcmarketing.dto.ShopTagDTO;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagCreateCmd;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagEditCmd;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagRemoveCmd;
import com.xtc.marketing.xtcmarketing.dto.query.ShopTagPageQry;
import com.xtc.marketing.xtcmarketing.exception.BizErrorCode;
import com.xtc.marketing.xtcmarketing.exception.BizException;
import com.xtc.marketing.xtcmarketing.executor.query.ShopTagGetQryExe;
import com.xtc.marketing.xtcmarketing.executor.query.TagGetQryExe;
import com.xtc.marketing.xtcmarketing.validator.TagValueValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店标签应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopTagAppServiceImpl implements ShopTagAppService {

    // executor
    private final ShopTagGetQryExe shopTagGetQryExe;
    private final TagGetQryExe tagGetQryExe;
    // infra
    private final ShopTagDao shopTagDao;
    private final ShopTagConverter shopTagConverter;

    @Override
    public PageResponse<ShopTagDTO> pageShopTags(ShopTagPageQry qry) {
        Page<ShopTagDO> page = shopTagDao.pageBy(qry);
        List<ShopTagDTO> records = shopTagConverter.doToDto(page.getRecords());
        return PageResponse.of(records, page.getTotalRow(), page.getPageSize(), page.getPageNumber());
    }

    @Override
    public ShopTagDTO getShopTagDetail(String shopId) {
        ShopTagDO shopTagDO = shopTagGetQryExe.byShopId(shopId);
        return shopTagConverter.doToDto(shopTagDO);
    }

    @Override
    public void createShopTag(ShopTagCreateCmd cmd) {
        // 查询门店标签
        ShopTagDO shopTagDO = shopTagGetQryExe.byShopId(cmd.getShopId());
        // 查询标签，并验证标签值在允许的范围内
        TagDO tagDO = tagGetQryExe.byTagCode(cmd.getTagCode());
        TagValueValidator.validateValueInRange(tagDO, cmd.getTagValue());
        // 门店已存在，将新标签数据添加到集合开头
        List<ShopTagDO.Tag> tags = shopTagDO.getTags() != null
                ? Lists.newArrayList(shopTagDO.getTags()) : Lists.newArrayListWithCapacity(1);
        tags.stream()
                .filter(tag -> tagDO.getTagCode().equals(tag.getTagCode()))
                .findAny()
                .ifPresentOrElse(
                        tag -> {
                            // 如果已存在相同标签代码，则修改标签值和tagTime
                            tag.setTagValue(cmd.getTagValue());
                            tag.setTagTime(LocalDateTime.now());
                        },
                        () -> {
                            // 如果不存在，则创建新标签信息
                            ShopTagDO.Tag newTag = ShopTagDO.Tag.builder()
                                    .tagValue(cmd.getTagValue())
                                    .tagTime(LocalDateTime.now())
                                    .tagType(tagDO.getTagType())
                                    .tagCode(tagDO.getTagCode())
                                    .tagName(tagDO.getTagName())
                                    .tagNo(tagDO.getTagNo())
                                    .build();
                            tags.addFirst(newTag);
                        }
                );
        // 保存门店标签数据
        shopTagDao.updateTagsByShopId(shopTagDO.getShopId(), tags);
    }

    @Override
    public void editShopTag(ShopTagEditCmd cmd) {
        // 查询门店标签
        ShopTagDO shopTagDO = shopTagGetQryExe.byShopId(cmd.getShopId());
        if (CollectionUtils.isEmpty(shopTagDO.getTags())) {
            throw BizException.of(BizErrorCode.B_SHOP_TAG_TagNotExists);
        }
        // 查询标签，并验证标签值在允许的范围内
        TagDO tagDO = tagGetQryExe.byTagCode(cmd.getTagCode());
        TagValueValidator.validateValueInRange(tagDO, cmd.getTagValue());
        // 从标签集合中筛选需要编辑的标签
        List<ShopTagDO.Tag> tags = Lists.newArrayList(shopTagDO.getTags());
        tags.stream()
                .filter(tag -> tagDO.getTagCode().equals(tag.getTagCode()))
                .findAny()
                .ifPresentOrElse(
                        tag -> {
                            // 修改标签值和更新时间
                            tag.setTagValue(cmd.getTagValue());
                            tag.setTagTime(LocalDateTime.now());
                        },
                        () -> {
                            throw BizException.of(BizErrorCode.B_SHOP_TAG_TagNotExists);
                        }
                );
        // 保存门店标签数据
        shopTagDao.updateTagsByShopId(shopTagDO.getShopId(), tags);
    }

    @Override
    public void removeShopTag(ShopTagRemoveCmd cmd) {
        // 查询门店标签
        ShopTagDO shopTagDO = shopTagGetQryExe.byShopId(cmd.getShopId());
        if (CollectionUtils.isEmpty(shopTagDO.getTags())) {
            throw BizException.of(BizErrorCode.B_SHOP_TAG_TagNotExists);
        }
        // 从标签集合中移除指定的标签
        List<ShopTagDO.Tag> tags = Lists.newArrayList(shopTagDO.getTags());
        boolean tagRemoved = tags.removeIf(tag -> cmd.getTagCode().equals(tag.getTagCode()));
        if (!tagRemoved) {
            throw BizException.of(BizErrorCode.B_SHOP_TAG_TagNotExists);
        }
        // 保存门店标签数据
        shopTagDao.updateTagsByShopId(shopTagDO.getShopId(), tags);
    }

}
