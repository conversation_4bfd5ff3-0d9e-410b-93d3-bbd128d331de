package com.xtc.marketing.xtcmarketing.service;

import com.xtc.marketing.xtcmarketing.dto.command.LoginCmd;
import com.xtc.marketing.xtcmarketing.exception.SysErrorCode;
import com.xtc.marketing.xtcmarketing.exception.SysException;
import com.xtc.marketing.xtcmarketing.rpc.wechat.WechatRpc;
import com.xtc.marketing.xtcmarketing.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 登录应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LoginAppServiceImpl implements LoginAppService {

    private final WechatRpc wechatRpc;

    /**
     * token 过期时间
     */
    private static final Duration TOKEN_EXPIRE_DURATION = Duration.ofDays(1);

    /* 登录配置 */
    @Value("${xtc.marketing.security.wechat-id:}")
    private String scrmWechatId;
    @Value("${xtc.marketing.security.issuer}")
    private String issuer;
    @Value("${xtc.marketing.security.system}")
    private String system;
    @Value("${xtc.marketing.security.private-key}")
    private String privateKey;

    @Override
    public String login(LoginCmd cmd) {
        // 获取企业微信用户id
        String userId = wechatRpc.getUserId(scrmWechatId, cmd.getCode())
                .orElseThrow(() -> SysException.of(SysErrorCode.S_LOGIN_LoginError, "获取企业微信用户id失败"));
        try {
            // 生成token
            return JwtUtil.generateToken(privateKey, issuer, system, TOKEN_EXPIRE_DURATION, userId);
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_LOGIN_LoginError, e.getMessage(), e);
        }
    }

}
