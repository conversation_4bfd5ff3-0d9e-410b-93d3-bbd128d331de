package com.xtc.marketing.xtcmarketing.api;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.dto.Response;
import com.xtc.marketing.dto.SingleResponse;
import com.xtc.marketing.xtcmarketing.dto.ShopTagDTO;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagCreateCmd;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagEditCmd;
import com.xtc.marketing.xtcmarketing.dto.command.ShopTagRemoveCmd;
import com.xtc.marketing.xtcmarketing.dto.query.ShopTagPageQry;
import com.xtc.marketing.xtcmarketing.service.ShopTagAppService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 门店标签接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class ShopTagApiController {

    private final ShopTagAppService shopTagAppService;

    /**
     * 门店标签分页列表
     *
     * @param qry 参数
     * @return 门店标签分页列表
     */
    @GetMapping("/shop-tags")
    public PageResponse<ShopTagDTO> pageShopTags(@Valid ShopTagPageQry qry) {
        return shopTagAppService.pageShopTags(qry);
    }

    /**
     * 门店标签详情
     *
     * @param shopId 门店id
     * @return 门店标签详情
     */
    @GetMapping("/shop-tags/{shopId}")
    public SingleResponse<ShopTagDTO> getShopTagDetail(@NotBlank @Length(max = 50) @PathVariable("shopId") String shopId) {
        ShopTagDTO shopTagDTO = shopTagAppService.getShopTagDetail(shopId);
        return SingleResponse.of(shopTagDTO);
    }

    /**
     * 新增门店标签
     *
     * @param cmd 参数
     */
    @PostMapping("/shop-tags")
    public Response createShopTag(@Valid @RequestBody ShopTagCreateCmd cmd) {
        shopTagAppService.createShopTag(cmd);
        return Response.buildSuccess();
    }

    /**
     * 编辑门店标签
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PutMapping("/shop-tags")
    public Response editShopTag(@Valid @RequestBody ShopTagEditCmd cmd) {
        shopTagAppService.editShopTag(cmd);
        return Response.buildSuccess();
    }

    /**
     * 删除门店标签
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @DeleteMapping("/shop-tags")
    public Response removeShopTag(@Valid ShopTagRemoveCmd cmd) {
        shopTagAppService.removeShopTag(cmd);
        return Response.buildSuccess();
    }

}
