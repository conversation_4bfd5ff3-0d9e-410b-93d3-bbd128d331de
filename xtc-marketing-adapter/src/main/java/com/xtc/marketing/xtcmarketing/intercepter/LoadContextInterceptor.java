package com.xtc.marketing.xtcmarketing.intercepter;

import com.auth0.jwt.exceptions.TokenExpiredException;
import com.xtc.marketing.xtcmarketing.annotation.AllowAnonymous;
import com.xtc.marketing.xtcmarketing.context.UserContext;
import com.xtc.marketing.xtcmarketing.dao.UserDao;
import com.xtc.marketing.xtcmarketing.dataobject.UserDO;
import com.xtc.marketing.xtcmarketing.exception.BizErrorCode;
import com.xtc.marketing.xtcmarketing.exception.BizException;
import com.xtc.marketing.xtcmarketing.exception.SysErrorCode;
import com.xtc.marketing.xtcmarketing.exception.SysException;
import com.xtc.marketing.xtcmarketing.util.AnnotationCatcher;
import com.xtc.marketing.xtcmarketing.util.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import javax.security.auth.login.CredentialNotFoundException;
import java.util.Optional;

/**
 * 加载用户上下文拦截器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class LoadContextInterceptor implements HandlerInterceptor {

    private final UserDao userDao;

    @Value("${xtc.marketing.security.issuer}")
    private String issuer;
    @Value("${xtc.marketing.security.system}")
    private String system;
    @Value("${xtc.marketing.security.public-key}")
    private String publicKey;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        // 过滤资源请求
        if (handler instanceof ResourceHttpRequestHandler) {
            return true;
        }
        // 判断是否需要数据处理
        AllowAnonymous allowAnonymous = AnnotationCatcher.catchHandlerAnnotation(handler, AllowAnonymous.class);
        if (allowAnonymous != null) {
            return true;
        }
        // 鉴权并获取用户id
        String userId = this.verifyTokenAndGetUserId(request);
        // 查询用户数据
        UserDO user = this.getUser(userId);
        // 设置用户上下文
        UserContext.User userContext = UserContext.User.builder()
                .userId(user.getUserId())
                .userName(user.getUserName())
                .build();
        UserContext.setUser(userContext);
        return true;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                                @NonNull Object handler, @Nullable Exception ex) {
        // 清除上下文数据，避免 ThreadLocal 内存泄漏
        UserContext.remove();
    }

    /**
     * 查询用户数据
     *
     * @param userId 用户id
     * @return 用户数据
     */
    private UserDO getUser(String userId) {
        UserDO userDO = userDao.getByUserId(userId)
                .orElseThrow(() -> BizException.of(BizErrorCode.B_USER_UserNotExists));
        if (BooleanUtils.isNotTrue(userDO.getEnabled())) {
            throw BizException.of(BizErrorCode.B_USER_UserDisabled);
        }
        return userDO;
    }

    /**
     * 鉴权并获取用户i
     *
     * @param request 请求
     * @return 用户id
     */
    private String verifyTokenAndGetUserId(HttpServletRequest request) {
        try {
            // 从请求头中获取 jwt
            Optional<String> jwt = JwtUtil.getJwt(request);
            if (jwt.isEmpty()) {
                throw new CredentialNotFoundException("请求头未携带鉴权数据");
            }
            // 验证 jwt 并获取用户id
            Optional<String> userId = JwtUtil.verifyAndGetUserId(publicKey, issuer, system, jwt.get());
            if (userId.isEmpty()) {
                throw new CredentialNotFoundException("鉴权数据未携带用户id");
            }
            return userId.get();
        } catch (TokenExpiredException e) {
            throw SysException.of(SysErrorCode.S_LOGIN_LoginError.getErrCode(), "登录已过期");
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_LOGIN_LoginError, e.getMessage(), e);
        }
    }

}
