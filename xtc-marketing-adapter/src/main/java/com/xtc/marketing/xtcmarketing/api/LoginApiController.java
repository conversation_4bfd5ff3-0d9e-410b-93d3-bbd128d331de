package com.xtc.marketing.xtcmarketing.api;

import com.xtc.marketing.dto.SingleResponse;
import com.xtc.marketing.xtcmarketing.dto.command.LoginCmd;
import com.xtc.marketing.xtcmarketing.service.LoginAppService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class LoginApiController {

    private final LoginAppService loginAppService;

    /**
     * 登录
     *
     * @param cmd 参数
     * @return token
     */
    @PostMapping("/login")
    public SingleResponse<String> login(@Valid @RequestBody LoginCmd cmd) {
        String token = loginAppService.login(cmd);
        return SingleResponse.of(token);
    }

}
